/* Custom theme colors */
:root {
    --flexcyon-primary: #FF6B6B;
    --flexcyon-secondary: #4ECDC4;
    --flexcyon-accent: #FFE66D;
    --base-01: #1A1A1A;
    --base-02: #2D2D2D;
    --base-03: #3D3D3D;
    --text-primary: #FFFFFF;
    --text-secondary: #B3B3B3;
}

/* Password toggle button */
.password-toggle {
    width: 36px !important;
    height: 36px !important;
    min-width: 36px !important;
    min-height: 36px !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}

.password-toggle img {
    width: 24px !important;
    height: 24px !important;
    object-fit: contain !important;
}

/* Gradio theme customization */
.gradio-container {
    background-color: var(--base-01) !important;
    color: var(--text-primary) !important;
    width: 60vw !important;
}

.gradio-interface {
    background-color: var(--base-01) !important;
}

.gradio-input {
    background-color: var(--base-02) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--base-03) !important;
}

.gradio-button {
    background-color: var(--flexcyon-primary) !important;
    color: var(--text-primary) !important;
}

.gradio-button:hover {
    background-color: var(--flexcyon-secondary) !important;
}

.gradio-chat {
    background-color: var(--base-02) !important;
}

.gradio-chat-message {
    background-color: var(--base-03) !important;
    color: var(--text-primary) !important;
}

/* Error messages */
.error-message {
    color: var(--flexcyon-primary) !important;
    background-color: rgba(255, 107, 107, 0.1) !important;
    border: 1px solid var(--flexcyon-primary) !important;
    padding: 10px !important;
    border-radius: 4px !important;
    margin: 10px 0 !important;
}

/* Success messages */
.success-message {
    color: var(--flexcyon-secondary) !important;
    background-color: rgba(78, 205, 196, 0.1) !important;
    border: 1px solid var(--flexcyon-secondary) !important;
    padding: 10px !important;
    border-radius: 4px !important;
    margin: 10px 0 !important;
}

/* Change password button styling */
#main_change_password_btn {
    background-color: var(--flexcyon-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--flexcyon-secondary) !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

#main_change_password_btn:hover {
    background-color: var(--flexcyon-primary) !important;
    border-color: var(--flexcyon-primary) !important;
    transform: translateY(-1px) !important;
}

/* Change password popup styling */
#change_password_popup {
    background-color: var(--base-02) !important;
    border: 1px solid var(--base-03) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 10px 0 !important;
}

#change_password_popup .popup-header {
    background-color: var(--base-03) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--base-03) !important;
}

#change_password_popup .popup-body {
    background-color: var(--base-02) !important;
    color: var(--text-primary) !important;
}

/* Password toggle buttons in change password popup */
#old_password_toggle,
#new_password_toggle,
#confirm_password_toggle {
    background-color: var(--base-03) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--base-03) !important;
    border-radius: 4px !important;
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

#old_password_toggle:hover,
#new_password_toggle:hover,
#confirm_password_toggle:hover {
    background-color: var(--flexcyon-secondary) !important;
    border-color: var(--flexcyon-secondary) !important;
}

/* Password requirements styling */
#password_requirements_info {
    background-color: rgba(78, 205, 196, 0.1) !important;
    border: 1px solid var(--flexcyon-secondary) !important;
    border-radius: 6px !important;
    padding: 12px !important;
    margin: 16px 0 !important;
    color: var(--text-secondary) !important;
    font-size: 14px !important;
}
