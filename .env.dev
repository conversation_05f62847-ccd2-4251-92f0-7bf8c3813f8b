# Copy over and create your own .env file

CHAT_DATA_PATH=data/modelling/data/
CLASSIFICATION_DATA_PATH=data/modelling/CNC chatbot/data classification/
DATABASE_PATH=data/vector_store/chroma_db/
LANGCHAIN_CHECKPOINT_PATH=data/memory_persistence/checkpoint.sqlite3
KEYWORDS_DATABANK_PATH=data/keyword/keywords_databank
CHAT_SESSIONS_PATH=data/chat_sessions/
EMBEDDING_MODEL=text-embedding-3-small
OPENAI_API_KEY=add_your_own_api_key

# Rate Limiting Configuration
# Default rate limits (requests per time window)
RATE_LIMIT_REQUESTS=60
RATE_LIMIT_WINDOW=60

# Chat-specific rate limits (1 request per second)
CHAT_RATE_LIMIT_REQUESTS=60
CHAT_RATE_LIMIT_WINDOW=60

# File upload rate limits (10 uploads per minute)
FILE_UPLOAD_RATE_LIMIT_REQUESTS=10
FILE_UPLOAD_RATE_LIMIT_WINDOW=60

# Audio processing rate limits (20 audio files per minute)
AUDIO_RATE_LIMIT_REQUESTS=20
AUDIO_RATE_LIMIT_WINDOW=60

# Authentication rate limits (5 attempts per 2 minutes)
AUTH_RATE_LIMIT_REQUESTS=5
AUTH_RATE_LIMIT_WINDOW=120
