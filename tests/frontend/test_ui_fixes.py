#!/usr/bin/env python3
"""
Test script for UI fixes:
1. "Chatbot not fully set up" message fix
2. 'bool' object has no attribute 'expandtabs' error fix
3. Search functionality fix
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
import asyncio

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def setup_test_environment():
    """Set up a temporary test environment."""
    test_dir = tempfile.mkdtemp(prefix="ui_fixes_test_")

    import backend

    original_get_chatbot_dir = backend.get_chatbot_dir

    def mock_get_chatbot_dir():
        return test_dir

    backend.get_chatbot_dir = mock_get_chatbot_dir
    backend.CHAT_SESSIONS_PATH = os.path.join(test_dir, "data", "chat_sessions")
    backend.USER_DB_PATH = os.path.join(test_dir, "data", "user_info", "users.json")

    os.makedirs(backend.CHAT_SESSIONS_PATH, exist_ok=True)
    os.makedirs(os.path.dirname(backend.USER_DB_PATH), exist_ok=True)

    return test_dir, original_get_chatbot_dir


def cleanup_test_environment(test_dir, original_get_chatbot_dir):
    """Clean up the test environment."""
    import backend

    backend.get_chatbot_dir = original_get_chatbot_dir
    shutil.rmtree(test_dir, ignore_errors=True)


def test_llm_ready_status():
    """Test that LLM ready status works correctly."""
    print("🤖 Testing LLM ready status...")

    try:
        from llm.chatModel import is_llm_ready, initialize_llm_and_db

        # Initialize the LLM
        initialize_llm_and_db()

        # Check if LLM is ready
        ready = is_llm_ready()
        print(f"  📊 LLM ready status: {ready}")

        if ready:
            print("  ✅ LLM is properly initialized")
        else:
            print("  ⚠️ LLM not ready - may affect chatbot responses")

        return True

    except Exception as e:
        print(f"❌ LLM ready test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_chatbot_response():
    """Test that chatbot responses work without 'not fully set up' message."""
    print("💬 Testing chatbot response generation...")

    try:
        import backend

        from tests.test_utils import get_default_test_user

        test_user = get_default_test_user()

        # Test chatbot response
        response_dict = asyncio.run(
            backend.get_chatbot_response(
                {
                    "username": test_user["username"],
                    "message": "Hello, this is a test message",
                    "history": [],
                    "chat_id": "test_chat",
                }
            )
        )

        response = response_dict.get("response", "")
        print(f"  📝 Response received: {response[:100]}...")

        # Check if it's the "not fully set up" message
        if "not fully set up" in response.lower():
            print("  ❌ Still getting 'not fully set up' message")
            return False
        elif "error" in response.lower():
            print(f"  ⚠️ Got error response: {response}")
            return True  # Error responses are acceptable for testing
        else:
            print("  ✅ Got proper chatbot response")
            return True

    except Exception as e:
        print(f"❌ Chatbot response test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_search_functionality():
    """Test that search functionality works without expandtabs error."""
    print("🔍 Testing search functionality...")

    try:
        import backend

        from tests.test_utils import get_default_test_user

        test_user = get_default_test_user()

        # Create test chats with content
        username = test_user["username"]

        # Create test chat with content
        chat_id = backend.create_and_persist_new_chat(username)
        user_folder = os.path.join(backend.CHAT_SESSIONS_PATH, username)
        chat_file = os.path.join(user_folder, f"{chat_id}.json")

        test_content = {
            "messages": [
                {"role": "user", "content": "How do I learn Python programming?"},
                {
                    "role": "assistant",
                    "content": "Python is a great language to start with. Here are some tips...",
                },
            ]
        }

        with open(chat_file, "w", encoding="utf-8") as f:
            json.dump(test_content, f, indent=2)

        print("  📝 Created test chat with content")

        # Test search functionality
        search_results = backend.search_chat_history("Python", username)

        print(f"  🔎 Search returned {len(search_results)} results")

        if isinstance(search_results, list):
            print("  ✅ Search returns proper list format")

            if search_results:
                result = search_results[0]
                if isinstance(result, dict) and "chat_id" in result:
                    print("  ✅ Search results have proper structure")
                    return True
                else:
                    print(f"  ❌ Search result structure invalid: {type(result)}")
                    return False
            else:
                print("  ⚠️ No search results found (may be normal)")
                return True
        else:
            print(f"  ❌ Search returned invalid type: {type(search_results)}")
            return False

    except Exception as e:
        print(f"❌ Search functionality test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_chatbot_ui_search():
    """Test the chatbot UI search functionality."""
    print("🎨 Testing chatbot UI search...")

    try:
        import gradio as gr
        from gradio_modules.chatbot import chatbot_ui

        from tests.test_utils import get_default_test_user

        test_user = get_default_test_user()

        # Test creating UI and search function
        with gr.Blocks():
            username_state = gr.State(test_user["username"])
            chat_history_state = gr.State([])
            chat_id_state = gr.State("")

            components = chatbot_ui(
                username_state, chat_history_state, chat_id_state, setup_events=False
            )

            # Should return 11 components
            assert len(components) == 11, (
                f"Expected 11 components, got {len(components)}"
            )

            # Test search function directly
            from gradio_modules.chatbot import handle_search

            # This should not cause expandtabs error
            result = handle_search(test_user["username"], "Python")

            print(f"  🔎 Search function returned: {type(result)}")

            if isinstance(result, str):
                print("  ✅ Search returns string (no expandtabs error)")
                return True
            else:
                print(f"  ❌ Search returned unexpected type: {type(result)}")
                return False

    except Exception as e:
        print(f"❌ Chatbot UI search test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def run_all_tests():
    """Run all UI fix tests."""
    print("🔧 Testing UI Fixes")
    print("=" * 50)
    print("Issues being tested:")
    print("  1. 'Chatbot not fully set up' message")
    print("  2. 'bool' object has no attribute 'expandtabs' error")
    print("  3. Search functionality not working")
    print("=" * 50)

    # Set up test environment
    test_dir, original_get_chatbot_dir = setup_test_environment()

    # Ensure default test user exists
    try:
        from tests.test_utils import ensure_default_test_user

        ensure_default_test_user()
        print("✅ Test user ready")
    except Exception as e:
        print(f"⚠️ Warning: Could not ensure test user exists: {e}")

    try:
        tests = [
            ("LLM Ready Status", test_llm_ready_status),
            ("Chatbot Response", test_chatbot_response),
            ("Search Functionality", test_search_functionality),
            ("Chatbot UI Search", test_chatbot_ui_search),
        ]

        results = {}
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name} test failed with exception: {e}")
                results[test_name] = False

        # Summary
        print("\n📊 Test Results Summary")
        print("-" * 30)
        passed = 0
        total = len(results)

        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name}: {status}")
            if result:
                passed += 1

        print(f"\nOverall: {passed}/{total} tests passed")

        if passed == total:
            print("\n🎉 All UI fixes working!")
            print("✅ No more 'chatbot not fully set up' messages")
            print("✅ No more 'expandtabs' errors")
            print("✅ Search functionality working")
            print("\n🚀 UI is ready for use!")
        else:
            print("\n❌ Some issues remain!")
            print("Please check the failed tests above.")

        return passed == total

    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return False
    finally:
        # Clean up
        cleanup_test_environment(test_dir, original_get_chatbot_dir)
        print("🧹 Test environment cleaned up")


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
