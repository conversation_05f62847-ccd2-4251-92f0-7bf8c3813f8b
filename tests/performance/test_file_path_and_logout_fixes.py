#!/usr/bin/env python3
"""
Test the file path and logout fixes.
Verifies that file classification reads from correct location and logout works properly.
"""

import sys
import os
from pathlib import Path
from llm.chatModel import initialize_llm_and_db

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

initialize_llm_and_db()


def test_file_classification_paths():
    """Test that file classification uses correct upload paths."""
    print("🔍 Testing File Classification Path Fixes...")

    try:
        from gradio_modules.file_classification import get_uploads_dir

        # Test production paths (default)
        os.environ.pop("TESTING", None)
        prod_upload_dir = get_uploads_dir("testuser")

        # Test paths (with TESTING=true)
        os.environ["TESTING"] = "true"
        test_upload_dir = get_uploads_dir("testuser")

        # Verify paths are different and correct
        assert prod_upload_dir != test_upload_dir, (
            "Production and test upload paths should be different"
        )
        assert "test_uploads" in test_upload_dir, (
            f"Test upload path should contain 'test_uploads': {test_upload_dir}"
        )
        assert "uploads" in prod_upload_dir and "test_uploads" not in prod_upload_dir, (
            f"Production upload path should contain 'uploads' but not 'test_uploads': {prod_upload_dir}"
        )

        # Verify directories are created
        assert os.path.exists(test_upload_dir), (
            f"Test upload directory should be created: {test_upload_dir}"
        )
        assert os.path.exists(prod_upload_dir), (
            f"Production upload directory should be created: {prod_upload_dir}"
        )

        print(f"  🏭 Production upload path: {prod_upload_dir}")
        print(f"  🧪 Test upload path: {test_upload_dir}")
        print("  ✅ File classification path fixes: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ File classification path fixes: FAILED - {e}")
        return False


def test_file_dropdown_functionality():
    """Test the file dropdown functionality."""
    print("🔍 Testing File Dropdown Functionality...")

    try:
        from gradio_modules.file_classification import get_uploaded_files, get_file_path

        # Set test environment
        os.environ["TESTING"] = "true"

        # Test with no files
        files = get_uploaded_files("testuser")
        assert isinstance(files, list), "Should return a list"

        # Test file path function
        file_path = get_file_path("testuser", "nonexistent.txt")
        assert file_path == "", "Should return empty string for non-existent file"

        # Test with empty username
        files_empty = get_uploaded_files("")
        assert files_empty == [], "Should return empty list for empty username"

        print("  📂 File dropdown functions working correctly")
        print("  ✅ File dropdown functionality: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ File dropdown functionality: FAILED - {e}")
        return False


def test_upload_history_functionality():
    """Test the upload history functionality."""
    print("🔍 Testing Upload History Functionality...")

    try:
        # Import the function from the file classification module
        import sys

        sys.path.append("gradio_modules")

        # We can't easily test the full upload history without creating the interface
        # But we can test the path logic
        from gradio_modules.file_classification import get_uploads_dir

        # Set test environment
        os.environ["TESTING"] = "true"

        # Test that upload directory is correctly determined
        upload_dir = get_uploads_dir("testuser")
        assert "test_uploads" in upload_dir, (
            f"Upload history should use test directory: {upload_dir}"
        )

        print(f"  📋 Upload history uses correct path: {upload_dir}")
        print("  ✅ Upload history functionality: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ Upload history functionality: FAILED - {e}")
        return False


def test_logout_handler_structure():
    """Test that the logout handler has the correct structure."""
    print("🔍 Testing Logout Handler Structure...")

    try:
        # Read the app.py file to verify logout handler structure
        app_py_path = project_root / "app.py"
        with open(app_py_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Check that logout handler returns the correct number of values
        logout_handler_found = "def handle_logout():" in content
        assert logout_handler_found, "Logout handler should exist"

        # Check that logout handler resets states
        state_reset_found = "False,  # Reset logged_in_state" in content
        assert state_reset_found, "Logout handler should reset logged_in_state"

        username_reset_found = '"",     # Reset username_state' in content
        assert username_reset_found, "Logout handler should reset username_state"

        # Check that logout button click has correct outputs
        logout_outputs_found = "logged_in_state, username_state," in content
        assert logout_outputs_found, "Logout button should update login states"

        print("  🔄 Logout handler properly resets states")
        print("  🔄 Logout button has correct outputs")
        print("  ✅ Logout handler structure: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ Logout handler structure: FAILED - {e}")
        return False


def test_login_state_management():
    """Test login state management components."""
    print("🔍 Testing Login State Management...")

    try:
        from gradio_modules.login_and_register import login_interface

        # Test that login interface returns the expected components
        components = login_interface(setup_events=False)

        # Should return a tuple with logged_in_state and username_state as first two elements
        assert len(components) >= 2, "Login interface should return multiple components"

        # The first two should be state components
        logged_in_state, username_state = components[0], components[1]

        # Verify they are Gradio State components
        import gradio as gr

        assert isinstance(logged_in_state, gr.State), (
            "First component should be logged_in_state"
        )
        assert isinstance(username_state, gr.State), (
            "Second component should be username_state"
        )

        # Verify initial values
        assert not logged_in_state.value, "Initial logged_in_state should be False"
        assert username_state.value == "", "Initial username_state should be empty"

        print("  🔐 Login interface returns correct state components")
        print("  🔐 Initial state values are correct")
        print("  ✅ Login state management: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ Login state management: FAILED - {e}")
        return False


def test_environment_cleanup():
    """Clean up test environment variables."""
    print("🔍 Testing Environment Cleanup...")

    try:
        # Remove test environment variable
        os.environ.pop("TESTING", None)

        # Verify it's removed
        testing_env = os.getenv("TESTING")
        assert testing_env is None, "TESTING environment variable should be removed"

        print("  🧹 Environment variables cleaned up")
        print("  ✅ Environment cleanup: PASSED")

        return True

    except Exception as e:
        print(f"  ❌ Environment cleanup: FAILED - {e}")
        return False


def run_file_path_and_logout_tests():
    """Run all file path and logout fix tests."""
    print("🚀 Running File Path and Logout Fix Tests")
    print("=" * 60)

    tests = [
        test_file_classification_paths,
        test_file_dropdown_functionality,
        test_upload_history_functionality,
        test_logout_handler_structure,
        test_login_state_management,
        test_environment_cleanup,
    ]

    results = []

    for test_func in tests:
        print(f"\n{'=' * 40}")
        try:
            success = test_func()
            results.append((test_func.__name__, success))
        except Exception as e:
            print(f"❌ {test_func.__name__}: FAILED - {e}")
            results.append((test_func.__name__, False))

    # Summary
    print(f"\n{'=' * 60}")
    print("📊 File Path and Logout Fix Test Results:")
    print(f"{'=' * 60}")

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {status} {test_name}")

    print(f"\n🎯 Summary: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All file path and logout fixes working correctly!")
        print("\n📋 Fixes Verified:")
        print("  ✅ File classification reads from correct upload directories")
        print("  ✅ Test/production path separation working")
        print("  ✅ File dropdown functionality implemented")
        print("  ✅ Upload history uses correct paths")
        print("  ✅ Logout handler properly resets all states")
        print("  ✅ Login state management working correctly")
        print("\n🛠️ Issues Fixed:")
        print("  🔧 File classification now reads from ~/.nypai-chatbot/uploads/")
        print("  🔧 Test files isolated to ~/.nypai-chatbot/test_uploads/")
        print("  🔧 Logout no longer crashes and properly resets states")
        print("  🔧 App no longer needs reload after logout")
        return True
    else:
        print("⚠️ Some file path and logout fix tests failed")
        return False


if __name__ == "__main__":
    success = run_file_path_and_logout_tests()
    sys.exit(0 if success else 1)
