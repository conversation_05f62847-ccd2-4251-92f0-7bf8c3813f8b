#!/usr/bin/env python3
"""
Final working demo showcasing all fixed and enhanced chatbot features:
✅ No more "chatbot not fully set up" messages
✅ No more 'expandtabs' errors
✅ Working search functionality
✅ Chat renaming functionality
✅ Smart auto-naming
✅ Optimized backend (no loops)
"""

import sys
from pathlib import Path
import gradio as gr
from gradio_modules.chatbot import chatbot_ui

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def create_final_demo():
    """Create a comprehensive demo of all working features."""

    with gr.Blocks(
        title="🚀 Complete Working Chatbot",
        css="""
        .feature-highlight {
            background: linear-gradient(90deg, #f0f9ff, #e0f2fe);
            border-left: 4px solid #0ea5e9;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .success-banner {
            background: linear-gradient(90deg, #f0fdf4, #dcfce7);
            border-left: 4px solid #22c55e;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .demo-section {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }
    """,
    ) as app:
        gr.Markdown("# 🎉 Complete Working Chatbot Demo")

        gr.Markdown(
            """
        <div class="success-banner">
        <h3>🎯 All Issues Fixed!</h3>
        <ul>
            <li><strong>✅ "Chatbot not fully set up"</strong> - Fixed LangGraph initialization</li>
            <li><strong>✅ 'expandtabs' errors</strong> - Fixed search result formatting</li>
            <li><strong>✅ Search not working</strong> - Fixed event handlers and function access</li>
            <li><strong>✅ Backend loops</strong> - Added initialization locks and flags</li>
        </ul>
        </div>
        """,
            elem_classes=["success-banner"],
        )

        gr.Markdown(
            """
        <div class="feature-highlight">
        <h3>🚀 Complete Feature Set:</h3>
        <ul>
            <li><strong>🔍 Smart Search:</strong> Search through all your chat history with intelligent matching</li>
            <li><strong>🧠 Auto-Naming:</strong> Chats get meaningful names based on your first message</li>
            <li><strong>🏷️ Chat Renaming:</strong> Rename any chat to organize your conversations better</li>
            <li><strong>📝 Live Updates:</strong> Chat dropdown updates automatically when you create new chats</li>
            <li><strong>💬 Persistent Chats:</strong> All conversations are saved and can be resumed</li>
            <li><strong>⚡ Fast Performance:</strong> Optimized backend with no infinite loops</li>
        </ul>
        </div>
        """,
            elem_classes=["feature-highlight"],
        )

        # Login simulation
        with gr.Row():
            username_input = gr.Textbox(
                label="Username",
                value="test_user",
                placeholder="Enter username to start",
            )
            login_btn = gr.Button("🚀 Start Demo", variant="primary")

        login_status = gr.Markdown("Enter a username and click 'Start Demo' to begin")

        # Complete chatbot interface (initially hidden)
        with gr.Column(visible=False) as chatbot_container:
            gr.Markdown("## 💬 Complete Chatbot Interface")

            # States
            username_state = gr.State("")
            chat_history_state = gr.State([])
            chat_id_state = gr.State("")

            # Complete chatbot UI with all features
            (
                chat_selector,
                new_chat_btn,
                chatbot,
                msg,
                send_btn,
                search_input,
                search_btn,
                search_results,
                rename_input,
                rename_btn,
                debug_md,
            ) = chatbot_ui(
                username_state, chat_history_state, chat_id_state, setup_events=True
            )

            # Feature demonstrations
            with gr.Accordion("🎯 How to Test All Features", open=True):
                gr.Markdown("""
                ### 🧠 Smart Auto-Naming
                1. **Don't select a chat** from the dropdown
                2. Type: "How do I learn Python programming?"
                3. Click Send - watch a new chat appear with name "Learn Python Programming"

                ### 🏷️ Chat Renaming
                1. Select any chat from the dropdown
                2. Type a new name in the rename field: "My Python Learning Chat"
                3. Click "🏷️ Rename" - watch the dropdown update instantly

                ### 🔍 Smart Search
                1. Create several chats with different topics
                2. Search for keywords like "Python", "programming", "learn"
                3. See intelligent results with context and previews

                ### 📝 Conversation Flow
                1. Send messages and see them persist automatically
                2. Switch between chats to see your conversation history
                3. Create new chats by just typing without selecting one
                """)

            # Demo instructions
            gr.Markdown(
                """
            <div class="demo-section">
            <h3>🎮 Quick Demo Steps:</h3>
            <ol>
                <li><strong>Auto-Name Test:</strong> Type "What is machine learning?" (no chat selected)</li>
                <li><strong>Create More:</strong> Try "How to build websites?" and "Database design tips"</li>
                <li><strong>Rename Test:</strong> Select a chat and rename it to something memorable</li>
                <li><strong>Search Test:</strong> Search for "machine" or "website" to find your chats</li>
                <li><strong>Chat Switch:</strong> Use dropdown to switch between conversations</li>
            </ol>
            <p><strong>🎯 Expected Results:</strong> Everything should work smoothly with no errors!</p>
            </div>
            """,
                elem_classes=["demo-section"],
            )

        def handle_login(username):
            """Handle demo login."""
            if not username.strip():
                return "❌ Please enter a username", gr.update(visible=False), ""

            return (
                f"✅ Demo started for: {username} - All features are working!",
                gr.update(visible=True),
                username.strip(),
            )

        # Login event
        login_btn.click(
            fn=handle_login,
            inputs=[username_input],
            outputs=[login_status, chatbot_container, username_state],
        )

        username_input.submit(
            fn=handle_login,
            inputs=[username_input],
            outputs=[login_status, chatbot_container, username_state],
        )

    return app


def main():
    """Run the complete working chatbot demo."""
    print("🎉 Complete Working Chatbot Demo")
    print("=" * 50)
    print("🎯 All Issues Fixed:")
    print("   ✅ No more 'chatbot not fully set up' messages")
    print("   ✅ No more 'expandtabs' errors")
    print("   ✅ Search functionality working perfectly")
    print("   ✅ Backend initialization optimized (no loops)")
    print("   ✅ Chat renaming working")
    print("   ✅ Smart auto-naming working")
    print()
    print("🚀 Complete Feature Set:")
    print("   🔍 Smart search through chat history")
    print("   🧠 Intelligent chat naming from first message")
    print("   🏷️ Easy chat renaming functionality")
    print("   📝 Auto-updating dropdown and live UI")
    print("   💬 Persistent conversation management")
    print("   ⚡ Fast, optimized performance")
    print()
    print("🌐 Opening demo in your browser...")
    print("=" * 50)

    try:
        app = create_final_demo()

        app.launch(
            debug=True,
            share=False,
            server_name="127.0.0.1",
            server_port=7866,  # Different port to avoid conflicts
        )

    except Exception as e:
        print(f"❌ Error launching demo: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
