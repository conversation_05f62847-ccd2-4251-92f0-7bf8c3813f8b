aiofiles==24.1.0
aiohttp==3.11.16
aiosqlite==0.21.0

# Security and authentication
bcrypt==4.3.0
cryptography==44.0.2

# Web framework and async (can be installed in parallel)
fastapi==0.115.12
filetype==1.2.0

# Gradio framework
gradio==5.34.0
gradio_client==1.10.3
# keybert  # Temporarily disabled due to torch compatibility issues on Alpine

# LangChain ecosystem (install together for better dependency resolution)
langchain>=0.3.24,<1.0.0
langchain-community==0.3.23
langchain-core>=0.3.56,<1.0.0
langchain-openai==0.3.12
langchain_experimental

# LangGraph
langgraph==0.3.30
langgraph-checkpoint-sqlite==2.0.7
nest_asyncio==1.6.0
nltk
# Core dependencies (install first - these are often pre-compiled)
numpy==1.26.4

# AI and ML libraries (these often have heavy compilation)
openai==1.72.0
pandas==2.2.3
pdf2image==1.17.0   # For PDF to image conversion if needed
pdfplumber==0.11.4  # Alternative to pdftotext - better text extraction
pdftotext
pillow==11.1.0
pydantic==2.11.2
pydantic-settings==2.8.1

# Document processing (install last as they may have complex dependencies)
pypdf==5.4.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
requests==2.32.3

# Database and data handling
SQLAlchemy==2.0.40
starlette==0.46.1
tiktoken==0.9.0
# unstructured  # Temporarily disabled due to tokenizers pre-release issues
uvicorn==0.34.0
